{"private": true, "name": "preact-app", "version": "0.0.0", "license": "MIT", "scripts": {"build": "cross-env NODE_OPTIONS=--openssl-legacy-provider preact build", "serve": "sirv build --port 8080 --cors --single", "dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider preact watch", "lint": "eslint src", "test": "jest"}, "eslintConfig": {"extends": "preact", "ignorePatterns": ["build/"]}, "devDependencies": {"cross-env": "^7.0.3", "enzyme": "^3.11.0", "enzyme-adapter-preact-pure": "^4.0.0", "eslint": "^8.30.0", "eslint-config-preact": "^1.3.0", "jest": "^27.0.0", "jest-preset-preact": "^4.0.5", "preact-cli": "^0.1.0", "sirv-cli": "^2.0.2"}, "dependencies": {"preact": "^10.11.3", "preact-render-to-string": "^5.2.6", "preact-router": "^3.2.1"}, "jest": {"preset": "jest-preset-preact", "setupFiles": ["<rootDir>/tests/__mocks__/browserMocks.js", "<rootDir>/tests/__mocks__/setupTests.js"]}}